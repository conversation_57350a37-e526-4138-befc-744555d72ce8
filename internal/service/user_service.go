package service

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
)

// ReferralTree represents a hierarchical view of referrals
type ReferralTree struct {
	User     *model.User     `json:"user"`
	Depth    int             `json:"depth"`
	Children []*ReferralTree `json:"children"`
	Stats    *ReferralStats  `json:"stats"`
}

// ReferralStats contains statistics for a referral node
type ReferralStats struct {
	DirectCount    int `json:"direct_count"`
	TotalDownlines int `json:"total_downlines"`
	MaxDepth       int `json:"max_depth"`
}

type UserServiceInterface interface {
	CreateUser(ctx context.Context, email, invitationCode, referrerCode string) (*model.User, error)
	CreateUserWallet(ctx context.Context, userID uuid.UUID, chain, name, walletAddress string, walletID, walletAccountID *uuid.UUID, walletType string) (*model.UserWallet, error)
	UpdateFirstLoginStatus(ctx context.Context, userID uuid.UUID) (*model.User, error)
	UpdateWalletExportStatus(ctx context.Context, userID uuid.UUID) (*model.User, error)
	UpdateUserInvitationCode(ctx context.Context, userID uuid.UUID, chain, name, walletAddress string, walletID, walletAccountID *uuid.UUID,
		walletType, invitationCode, email string, isFirstLogin, isExportedWallet bool) (*model.User, error)
	GetUserByID(ctx context.Context, id uuid.UUID) (*model.User, error)
	GetUserByEmail(ctx context.Context, email string) (*model.User, error)
	GetUserWallets(ctx context.Context, userID uuid.UUID) ([]model.UserWallet, error)
	GetReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error)
	GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error)
	GetDownlines(ctx context.Context, userID uuid.UUID) ([]model.Referral, error)
	GetUserByInvitationCode(ctx context.Context, invitationCode string) (*model.User, error)
	CreateUserWithReferral(ctx context.Context, referrerID uuid.UUID, userID string) error
	GetDownlinesWithLimit(ctx context.Context, userID uuid.UUID, maxLevel int) ([]model.Referral, error)
	GetReferralTree(ctx context.Context, userID uuid.UUID) (*ReferralTree, error)
	ValidateReferralEligibility(ctx context.Context, userID, referrerID uuid.UUID) error
}

type UserService struct {
	userRepo repo.UserRepositoryInterface
}

func NewUserService() UserServiceInterface {
	return &UserService{
		userRepo: repo.NewUserRepository(),
	}
}

func (s *UserService) CreateUser(ctx context.Context, email, invitationCode, referrerCode string) (*model.User, error) {
	// Check if user already exists
	existingUser, err := s.userRepo.GetByEmail(ctx, email)
	if err == nil && existingUser != nil {
		return nil, fmt.Errorf("user with email %s already exists", email)
	}

	// Validate invitation code if provided
	if invitationCode != "" {
		if len(invitationCode) < 5 || len(invitationCode) > 15 {
			return nil, fmt.Errorf("invitation code must be 5-15 characters long")
		}
	}

	user := &model.User{
		Email:            email,
		IsFirstLogin:     true,
		IsExportedWallet: false,
		InvitationCode:   nil, // Will be set if invitationCode is provided
	}

	// Set invitation code if provided
	if invitationCode != "" {
		user.InvitationCode = &invitationCode
	}

	// Create user
	if err := s.userRepo.Create(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Handle referral if referrer code is provided
	if referrerCode != "" {
		if err := s.createReferralRelationship(ctx, user.ID, referrerCode); err != nil {
			// Log error but don't fail user creation
			// In production, you might want to handle this differently
			fmt.Printf("Failed to create referral relationship: %v\n", err)
		}
	}

	return user, nil
}

func (s *UserService) CreateUserWallet(ctx context.Context, userID uuid.UUID, chain, name, walletAddress string, walletID, walletAccountID *uuid.UUID, walletType string) (*model.UserWallet, error) {
	wallet := &model.UserWallet{
		UserID:          userID,
		Chain:           chain,
		Name:            name,
		WalletAddress:   walletAddress,
		WalletID:        walletID,
		WalletAccountID: walletAccountID,
		WalletType:      walletType,
	}

	if err := s.userRepo.CreateUserWallet(ctx, wallet); err != nil {
		return nil, fmt.Errorf("failed to create user wallet: %w", err)
	}

	return wallet, nil
}

func (s *UserService) UpdateFirstLoginStatus(ctx context.Context, userID uuid.UUID) (*model.User, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	user.IsFirstLogin = false
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return user, nil
}

func (s *UserService) UpdateWalletExportStatus(ctx context.Context, userID uuid.UUID) (*model.User, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	user.IsExportedWallet = true
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return user, nil
}

func (s *UserService) UpdateUserInvitationCode(ctx context.Context, userID uuid.UUID, chain, name, walletAddress string,
	walletID, walletAccountID *uuid.UUID, walletType, invitationCode, email string, isFirstLogin, isExportedWallet bool) (*model.User, error) {
	// Validate invitation code format
	if len(invitationCode) < 5 || len(invitationCode) > 15 {
		return nil, fmt.Errorf("invitation code must be 5-15 characters long")
	}

	newUser := &model.User{
		ID:             userID,
		InvitationCode: &invitationCode,
	}
	if email == "" {
		newUser.Email = userID.String() + "example.com"
	}

	if err := s.userRepo.Create(ctx, newUser); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}
	newUserWallet := &model.UserWallet{
		ID:              uuid.New(),
		UserID:          userID,
		Chain:           chain,
		Name:            name,
		WalletAddress:   walletAddress,
		WalletID:        walletID,
		WalletAccountID: walletAccountID,
		WalletType:      walletType,
	}
	if err := s.userRepo.CreateWallet(ctx, newUserWallet); err != nil {
		return nil, fmt.Errorf("failed to create user wallet: %w", err)
	}

	return newUser, nil
}

func (s *UserService) GetUserByID(ctx context.Context, id uuid.UUID) (*model.User, error) {
	return s.userRepo.GetByID(ctx, id)
}

func (s *UserService) GetUserByEmail(ctx context.Context, email string) (*model.User, error) {
	return s.userRepo.GetByEmail(ctx, email)
}

func (s *UserService) GetUserWallets(ctx context.Context, userID uuid.UUID) ([]model.UserWallet, error) {
	return s.userRepo.GetUserWallets(ctx, userID)
}

func (s *UserService) GetReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error) {
	return s.userRepo.GetReferralInfo(ctx, userID)
}

func (s *UserService) GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error) {
	return s.userRepo.GetReferralSnapshot(ctx, userID)
}

func (s *UserService) GetDownlines(ctx context.Context, userID uuid.UUID) ([]model.Referral, error) {
	return s.userRepo.GetDownlines(ctx, userID)
}

func (s *UserService) createReferralRelationship(ctx context.Context, userID uuid.UUID, referrerCode string) error {
	// Validate referrer code format
	if len(referrerCode) < 5 || len(referrerCode) > 15 {
		return fmt.Errorf("invalid referrer code format: must be 5-15 characters")
	}

	// Find referrer by invitation code
	referrer, err := s.userRepo.GetByInvitationCode(ctx, referrerCode)
	if err != nil {
		return fmt.Errorf("referrer not found with code %s: %w", referrerCode, err)
	}

	return s.createReferralRelationshipByUUID(ctx, userID, referrer.ID)
}

func (s *UserService) createReferralRelationshipByID(ctx context.Context, userID uuid.UUID, referrerID string) error {
	referrerUUID, err := uuid.Parse(referrerID)
	if err != nil {
		return fmt.Errorf("invalid referrer ID: %w", err)
	}

	return s.createReferralRelationshipByUUID(ctx, userID, referrerUUID)
}

// createReferralRelationshipByUUID creates a referral relationship with proper validation
func (s *UserService) createReferralRelationshipByUUID(ctx context.Context, userID, referrerUUID uuid.UUID) error {
	// Basic validation
	if referrerUUID == userID {
		return fmt.Errorf("cannot refer yourself")
	}

	// Check if referrer exists
	_, err := s.userRepo.GetByID(ctx, referrerUUID)
	if err != nil {
		return fmt.Errorf("referrer not found with ID %s: %w", referrerUUID.String(), err)
	}

	// Check if referral relationship already exists
	existingReferral, err := s.userRepo.GetReferralInfoSafe(ctx, userID)
	if err == nil && existingReferral != nil {
		return fmt.Errorf("referral relationship already exists for this user")
	}

	// Validate that this won't create a cycle
	if err := s.userRepo.ValidateReferralChain(ctx, userID, referrerUUID); err != nil {
		return fmt.Errorf("referral validation failed: %w", err)
	}

	// Get referrer's current depth to calculate new user's depth
	referrerDepth, err := s.userRepo.GetReferralDepth(ctx, referrerUUID)
	if err != nil {
		// If referrer has no referral info, they are at depth 0
		referrerDepth = 0
	}

	// Calculate new user's depth (referrer's depth + 1)
	newDepth := referrerDepth + 1
	if newDepth > model.MaxReferralDepth {
		return fmt.Errorf("referral depth would exceed maximum allowed depth of %d", model.MaxReferralDepth)
	}

	// Create the referral relationship
	referral := &model.Referral{
		UserID:     userID,
		ReferrerID: &referrerUUID,
		Depth:      newDepth,
	}

	// Validate the referral data
	if err := referral.Validate(); err != nil {
		return fmt.Errorf("referral validation failed: %w", err)
	}

	if err := s.userRepo.CreateReferral(ctx, referral); err != nil {
		return fmt.Errorf("failed to create referral: %w", err)
	}

	return nil
}

func (s *UserService) GetUserByInvitationCode(ctx context.Context, invitationCode string) (*model.User, error) {
	return s.userRepo.GetByInvitationCode(ctx, invitationCode)
}

func (s *UserService) CreateUserWithReferral(ctx context.Context, referrerID uuid.UUID, userID string) error {
	idParse, err := uuid.Parse(userID)
	if err != nil {
		return fmt.Errorf("invalid user ID: %w", err)
	}

	if err := s.createReferralRelationshipByID(ctx, idParse, referrerID.String()); err != nil {
		return err
	}
	return nil
}

// GetDownlinesWithLimit gets downlines with explicit level limit
func (s *UserService) GetDownlinesWithLimit(ctx context.Context, userID uuid.UUID, maxLevel int) ([]model.Referral, error) {
	return s.userRepo.GetDownlinesWithLimit(ctx, userID, maxLevel)
}

// ValidateReferralEligibility validates if a user can be referred by another user
func (s *UserService) ValidateReferralEligibility(ctx context.Context, userID, referrerID uuid.UUID) error {
	// Basic validation
	if referrerID == userID {
		return fmt.Errorf("user cannot refer themselves")
	}

	// Check if referrer exists
	_, err := s.userRepo.GetByID(ctx, referrerID)
	if err != nil {
		return fmt.Errorf("referrer not found: %w", err)
	}

	// Check if user already has a referrer
	existingReferral, err := s.userRepo.GetReferralInfoSafe(ctx, userID)
	if err == nil && existingReferral != nil {
		return fmt.Errorf("user already has a referrer")
	}

	// Validate that this won't create a cycle
	if err := s.userRepo.ValidateReferralChain(ctx, userID, referrerID); err != nil {
		return fmt.Errorf("referral would create a cycle: %w", err)
	}

	// Check depth limit
	referrerDepth, err := s.userRepo.GetReferralDepth(ctx, referrerID)
	if err != nil {
		referrerDepth = 0 // If no referral info, assume depth 0
	}

	if referrerDepth+1 > model.MaxReferralDepth {
		return fmt.Errorf("referral would exceed maximum depth of %d", model.MaxReferralDepth)
	}

	return nil
}

// GetReferralTree builds a hierarchical tree of referrals
func (s *UserService) GetReferralTree(ctx context.Context, userID uuid.UUID) (*ReferralTree, error) {
	// Get user info
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Get user's referral depth
	depth, err := s.userRepo.GetReferralDepth(ctx, userID)
	if err != nil {
		depth = 0
	}

	// Build the tree recursively
	tree := &ReferralTree{
		User:     user,
		Depth:    depth,
		Children: make([]*ReferralTree, 0),
		Stats:    &ReferralStats{},
	}

	// Get direct referrals (limited to prevent infinite recursion)
	downlines, err := s.userRepo.GetDownlinesWithLimit(ctx, userID, 3) // Limit to 3 levels for tree view
	if err != nil {
		return tree, nil // Return partial tree on error
	}

	// Build children trees
	directReferrals := make(map[uuid.UUID]bool)
	for _, downline := range downlines {
		if downline.Depth == 1 { // Direct referrals only
			directReferrals[downline.UserID] = true
			childTree, err := s.buildReferralSubTree(ctx, downline.UserID, depth+1, 2) // Limit recursion
			if err == nil {
				tree.Children = append(tree.Children, childTree)
			}
		}
	}

	// Calculate stats
	tree.Stats.DirectCount = len(tree.Children)
	tree.Stats.TotalDownlines = len(downlines)
	tree.Stats.MaxDepth = s.calculateMaxDepth(tree)

	return tree, nil
}

// buildReferralSubTree builds a subtree for referral hierarchy (with recursion limit)
func (s *UserService) buildReferralSubTree(ctx context.Context, userID uuid.UUID, currentDepth, maxRecursion int) (*ReferralTree, error) {
	if maxRecursion <= 0 {
		return nil, fmt.Errorf("max recursion reached")
	}

	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	tree := &ReferralTree{
		User:     user,
		Depth:    currentDepth,
		Children: make([]*ReferralTree, 0),
		Stats:    &ReferralStats{},
	}

	// Get direct children only (depth 1 from this user)
	downlines, err := s.userRepo.GetDownlinesWithLimit(ctx, userID, 1)
	if err != nil {
		return tree, nil
	}

	// Build children recursively
	for _, downline := range downlines {
		if downline.Depth == 1 { // Only direct children
			childTree, err := s.buildReferralSubTree(ctx, downline.UserID, currentDepth+1, maxRecursion-1)
			if err == nil {
				tree.Children = append(tree.Children, childTree)
			}
		}
	}

	tree.Stats.DirectCount = len(tree.Children)
	tree.Stats.TotalDownlines = len(downlines)
	tree.Stats.MaxDepth = s.calculateMaxDepth(tree)

	return tree, nil
}

// calculateMaxDepth calculates the maximum depth in a referral tree
func (s *UserService) calculateMaxDepth(tree *ReferralTree) int {
	if len(tree.Children) == 0 {
		return tree.Depth
	}

	maxChildDepth := tree.Depth
	for _, child := range tree.Children {
		childMaxDepth := s.calculateMaxDepth(child)
		if childMaxDepth > maxChildDepth {
			maxChildDepth = childMaxDepth
		}
	}

	return maxChildDepth
}
