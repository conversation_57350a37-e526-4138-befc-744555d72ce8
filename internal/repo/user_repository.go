package repo

import (
	"context"
	"errors"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

type UserRepositoryInterface interface {
	Create(ctx context.Context, user *model.User) error
	CreateWallet(ctx context.Context, wallet *model.UserWallet) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.User, error)
	GetByEmail(ctx context.Context, email string) (*model.User, error)
	GetByInvitationCode(ctx context.Context, code string) (*model.User, error)
	Update(ctx context.Context, user *model.User) error
	Delete(ctx context.Context, id uuid.UUID) error
	GetUserWallets(ctx context.Context, userID uuid.UUID) ([]model.UserWallet, error)
	CreateUserWallet(ctx context.Context, wallet *model.UserWallet) error
	GetReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error)
	GetDirectReferral(ctx context.Context, userID, referrerID uuid.UUID) (*model.Referral, error)
	GetAllReferrals(ctx context.Context, userID uuid.UUID) ([]model.Referral, error)
	CreateReferral(ctx context.Context, referral *model.Referral) error
	GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error)
	UpdateReferralSnapshot(ctx context.Context, snapshot *model.ReferralSnapshot) error
	GetDownlines(ctx context.Context, userID uuid.UUID) ([]model.Referral, error)
	GetDownlinesWithLimit(ctx context.Context, userID uuid.UUID, maxLevel int) ([]model.Referral, error)
	GetReferrerByUserID(ctx context.Context, referrerID uuid.UUID) (model.Referral, error)
	GetReferrerByReferrerID(ctx context.Context, referrerID uuid.UUID) (model.Referral, error)
	GetReferralInfoSafe(ctx context.Context, userID uuid.UUID) (*model.Referral, error)
	ValidateReferralChain(ctx context.Context, userID, referrerID uuid.UUID) error
	GetReferralDepth(ctx context.Context, userID uuid.UUID) (int, error)
}

type UserRepository struct {
	db *gorm.DB
}

func NewUserRepository() UserRepositoryInterface {
	return &UserRepository{
		db: global.GVA_DB,
	}
}

func (r *UserRepository) Create(ctx context.Context, user *model.User) error {
	return r.db.WithContext(ctx).Create(user).Error
}
func (r *UserRepository) CreateWallet(ctx context.Context, wallet *model.UserWallet) error {
	return r.db.WithContext(ctx).Create(wallet).Error
}

func (r *UserRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.User, error) {
	var user model.User
	err := r.db.Debug().WithContext(ctx).
		First(&user, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *UserRepository) GetByEmail(ctx context.Context, email string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).
		Preload("Wallets").
		Preload("Referrals").
		Preload("Referrals.Referrer").
		Preload("ReferralSnapshot").
		Preload("ReferredUsers").
		First(&user, "email = ?", email).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *UserRepository) GetByInvitationCode(ctx context.Context, code string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).First(&user, "invitation_code = ?", code).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *UserRepository) Update(ctx context.Context, user *model.User) error {
	return r.db.WithContext(ctx).Save(user).Error
}

func (r *UserRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&model.User{}, "id = ?", id).Error
}

func (r *UserRepository) GetUserWallets(ctx context.Context, userID uuid.UUID) ([]model.UserWallet, error) {
	var wallets []model.UserWallet
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Find(&wallets).Error
	return wallets, err
}

func (r *UserRepository) CreateUserWallet(ctx context.Context, wallet *model.UserWallet) error {
	return r.db.WithContext(ctx).Create(wallet).Error
}

func (r *UserRepository) GetReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error) {
	var referral model.Referral
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Referrer").
		// Removed "Referrer.Referral" preload to prevent infinite loops
		First(&referral, "user_id = ?", userID).Error
	if err != nil {
		return nil, err
	}
	return &referral, nil
}

// GetReferralInfoSafe gets referral info without any preloads to prevent cycles
func (r *UserRepository) GetReferralInfoSafe(ctx context.Context, userID uuid.UUID) (*model.Referral, error) {
	var referral model.Referral
	err := r.db.WithContext(ctx).
		First(&referral, "user_id = ?", userID).Error
	if err != nil {
		return nil, err
	}
	return &referral, nil
}

func (r *UserRepository) GetDirectReferral(ctx context.Context, userID, referrerID uuid.UUID) (*model.Referral, error) {
	var referral model.Referral
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Referrer").
		First(&referral, "user_id = ? AND referrer_id = ?", userID, referrerID).Error
	if err != nil {
		return nil, err
	}
	return &referral, nil
}

func (r *UserRepository) GetAllReferrals(ctx context.Context, userID uuid.UUID) ([]model.Referral, error) {
	var referrals []model.Referral
	err := r.db.WithContext(ctx).
		Preload("User").
		Preload("Referrer").
		Where("user_id = ?", userID).
		Find(&referrals).Error
	if err != nil {
		return nil, err
	}
	return referrals, nil
}

func (r *UserRepository) GetReferrerByUserID(ctx context.Context, userID uuid.UUID) (model.Referral, error) {
	var referral model.Referral
	err := r.db.WithContext(ctx).
		Preload("User").
		First(&referral, "referrer_id = ?", userID).Error
	if err != nil {
		return model.Referral{}, err
	}
	return referral, nil
}

func (r *UserRepository) GetReferrerByReferrerID(ctx context.Context, referrerID uuid.UUID) (model.Referral, error) {
	var referral model.Referral
	err := r.db.WithContext(ctx).
		Preload("User").
		First(&referral, "user_id = ?", referrerID).Error
	if err != nil {
		return model.Referral{}, err
	}
	return referral, nil
}

func (r *UserRepository) CreateReferral(ctx context.Context, referral *model.Referral) error {
	return r.db.WithContext(ctx).Create(referral).Error
}

func (r *UserRepository) GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error) {
	var snapshot model.ReferralSnapshot
	err := r.db.WithContext(ctx).
		Preload("User").
		First(&snapshot, "user_id = ?", userID).Error
	if err != nil {
		return nil, err
	}
	return &snapshot, nil
}

func (r *UserRepository) UpdateReferralSnapshot(ctx context.Context, snapshot *model.ReferralSnapshot) error {
	return r.db.WithContext(ctx).Save(snapshot).Error
}

func (r *UserRepository) GetDownlines(ctx context.Context, userID uuid.UUID) ([]model.Referral, error) {
	var downlines []model.Referral

	// Execute the recursive CTE query with depth limit and cycle detection
	query := `
		WITH RECURSIVE downlines AS (
			-- Base case: direct referrals
			SELECT
				user_id,
				referrer_id,
				depth,
				ARRAY[user_id] as path,
				1 as level
			FROM referrals
			WHERE referrer_id = $1

			UNION ALL

			-- Recursive case: indirect referrals with safeguards
			SELECT
				r.user_id,
				r.referrer_id,
				r.depth,
				d.path || r.user_id,
				d.level + 1
			FROM referrals r
			INNER JOIN downlines d ON r.referrer_id = d.user_id
			WHERE d.level < 7  -- Limit to 7 levels maximum
			  AND NOT (r.user_id = ANY(d.path))  -- Prevent cycles
		)
		SELECT user_id, referrer_id, depth FROM downlines
		ORDER BY level, user_id
	`

	err := r.db.WithContext(ctx).Raw(query, userID).Scan(&downlines).Error
	return downlines, err
}

// GetDownlinesWithLimit gets downlines with explicit level limit
func (r *UserRepository) GetDownlinesWithLimit(ctx context.Context, userID uuid.UUID, maxLevel int) ([]model.Referral, error) {
	if maxLevel <= 0 || maxLevel > model.MaxReferralDepth {
		maxLevel = model.MaxReferralDepth
	}

	var downlines []model.Referral

	query := `
		WITH RECURSIVE downlines AS (
			-- Base case: direct referrals
			SELECT
				user_id,
				referrer_id,
				depth,
				ARRAY[user_id] as path,
				1 as level
			FROM referrals
			WHERE referrer_id = $1

			UNION ALL

			-- Recursive case: indirect referrals with safeguards
			SELECT
				r.user_id,
				r.referrer_id,
				r.depth,
				d.path || r.user_id,
				d.level + 1
			FROM referrals r
			INNER JOIN downlines d ON r.referrer_id = d.user_id
			WHERE d.level < $2  -- Configurable level limit
			  AND NOT (r.user_id = ANY(d.path))  -- Prevent cycles
		)
		SELECT user_id, referrer_id, depth FROM downlines
		ORDER BY level, user_id
	`

	err := r.db.WithContext(ctx).Raw(query, userID, maxLevel).Scan(&downlines).Error
	return downlines, err
}

// ValidateReferralChain validates that creating a referral won't create a cycle
func (r *UserRepository) ValidateReferralChain(ctx context.Context, userID, referrerID uuid.UUID) error {
	// Check if referrer is already in user's downline (would create cycle)
	query := `
		WITH RECURSIVE upline AS (
			SELECT user_id, referrer_id, 1 as level
			FROM referrals
			WHERE user_id = $1

			UNION ALL

			SELECT r.user_id, r.referrer_id, u.level + 1
			FROM referrals r
			INNER JOIN upline u ON r.user_id = u.referrer_id
			WHERE u.level < 7  -- Prevent infinite recursion
		)
		SELECT COUNT(*) FROM upline WHERE referrer_id = $2
	`

	var count int64
	err := r.db.WithContext(ctx).Raw(query, referrerID, userID).Scan(&count).Error
	if err != nil {
		return err
	}

	if count > 0 {
		return errors.New("creating this referral would create a cycle")
	}

	return nil
}

// GetReferralDepth gets the current depth of a user in the referral chain
func (r *UserRepository) GetReferralDepth(ctx context.Context, userID uuid.UUID) (int, error) {
	query := `
		WITH RECURSIVE upline AS (
			SELECT user_id, referrer_id, 0 as depth
			FROM referrals
			WHERE user_id = $1

			UNION ALL

			SELECT r.user_id, r.referrer_id, u.depth + 1
			FROM referrals r
			INNER JOIN upline u ON r.user_id = u.referrer_id
			WHERE u.depth < 7  -- Prevent infinite recursion
		)
		SELECT COALESCE(MAX(depth), 0) FROM upline
	`

	var depth int
	err := r.db.WithContext(ctx).Raw(query, userID).Scan(&depth).Error
	return depth, err
}
