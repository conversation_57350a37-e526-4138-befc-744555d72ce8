package model

import (
	"errors"
	"time"

	"github.com/google/uuid"
)

const (
	// MaxReferralDepth defines the maximum depth allowed in referral hierarchy
	MaxReferralDepth = 7

	// DirectReferralDepth is the depth for direct referrals
	DirectReferralDepth = 1
)

// Referral represents the referrals table
type Referral struct {
	ID         uint       `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID     uuid.UUID  `gorm:"type:uuid;not null;unique;index" json:"user_id"`
	ReferrerID *uuid.UUID `gorm:"type:uuid;index" json:"referrer_id"`
	Depth      int        `gorm:"default:1;check:depth >= 1 AND depth <= 7" json:"depth"` // 1 means direct referral, max 7 levels
	CreatedAt  time.Time  `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`

	// Relationships - avoid deep preloading to prevent infinite loops
	User     User  `gorm:"foreignKey:UserID;references:ID" json:"user"`
	Referrer *User `gorm:"foreignKey:ReferrerID;references:ID" json:"referrer,omitempty"`
}

// TableName specifies the table name for Referral
func (Referral) TableName() string {
	return "referrals"
}

// Validate validates the referral data
func (r *Referral) Validate() error {
	if r.UserID == uuid.Nil {
		return errors.New("user_id cannot be empty")
	}

	if r.ReferrerID != nil && *r.ReferrerID == r.UserID {
		return errors.New("user cannot refer themselves")
	}

	if r.Depth < DirectReferralDepth || r.Depth > MaxReferralDepth {
		return errors.New("depth must be between 1 and 7")
	}

	return nil
}

// IsDirectReferral checks if this is a direct referral
func (r *Referral) IsDirectReferral() bool {
	return r.Depth == DirectReferralDepth
}

// IsMaxDepth checks if this referral is at maximum allowed depth
func (r *Referral) IsMaxDepth() bool {
	return r.Depth >= MaxReferralDepth
}
