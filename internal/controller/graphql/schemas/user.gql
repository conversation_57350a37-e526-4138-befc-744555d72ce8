type User {
  id: ID!
  email: String
  isFirstLogin: Boolean!
  isExportedWallet: Boolean!
  invitationCode: String
  createdAt: Time!
  updatedAt: Time!
  wallets: [UserWallet!]!
  referral: Referral
  referralSnapshot: ReferralSnapshot
  referrals: [Referral!]!
}

type UserWallet {
  id: ID!
  userId: ID!
  chain: String!
  name: String
  walletAddress: String!
  walletId: ID
  walletAccountId: ID
  walletType: WalletType
  createdAt: Time!
  updatedAt: Time!
  user: User!
}

type Referral {
  id: Int!
  userId: ID!
  referrerId: ID
  depth: Int!
  createdAt: Time!
  user: User!
  referrer: User
}

type ReferralSnapshot {
  userId: ID!
  directCount: Int!
  totalDownlineCount: Int!
  totalVolumeUsd: Float!
  totalRewardsDistributed: Float!
  user: User!
}

enum WalletType {
  EMBEDDED
  MANAGED
}

input CreateUserInput {
  email: String!
  invitationCode: String
  referrerCode: String
}

input CreateUserInvitationCodeInput {
  userId: ID!
  invitationCode: String!
  email: String
  isFirstLogin: Boolean
  isExportedWallet: Boolean
  chain: String
  name: String
  walletAddress: String
  walletId: ID
  walletAccountId: ID
  walletType: WalletType!
}

input CreateUserWalletInput {
  userId: ID!
  chain: String!
  name: String
  walletAddress: String!
  walletId: ID
  walletAccountId: ID
  walletType: WalletType!
}


type CreateUserResponse {
  user: User!
  #token: String!
  success: Boolean!
  message: String!
}

type CreateUserWalletResponse {
  wallet: UserWallet!
  success: Boolean!
  message: String!
}

input CreateUserWithReferralInput {
  userId: ID!
  invitationCode: String!
}

type Query {
  # Get user by ID
  user(id: ID!): User @auth

  # Get user by email
  userByEmail(email: String!): User @auth

  # Get user wallets
  userWallets(userId: ID!): [UserWallet!]! @auth

  # Get referral information
  referralInfo(userId: ID!): Referral @auth

  # Get referral snapshot
  referralSnapshot(userId: ID!): ReferralSnapshot @auth

  # Get all downlines (recursive) with depth limit
  downlines(userId: ID!): [Referral!]! @auth

  # Get downlines with custom limit
  downlinesWithLimit(userId: ID!, maxLevel: Int!): [Referral!]! @auth

  # Get referral tree (hierarchical view)
  referralTree(userId: ID!): ReferralTree @auth

  # Get user's invitation code
  getUserInvitationCode(userId: ID!): String @auth
}

input UpdateInvitationCodeInput {
  userId: ID!
  invitationCode: String!
}

type UpdateInvitationCodeResponse {
  user: User!
  success: Boolean!
  message: String!
}

type GenerateInvitationCodeResponse {
  user: User!
  success: Boolean!
  message: String!
}

# Referral Tree Types
type ReferralTree {
  user: User!
  depth: Int!
  children: [ReferralTree!]!
  stats: ReferralStats!
}

type ReferralStats {
  directCount: Int!
  totalDownlines: Int!
  maxDepth: Int!
}

type Mutation {
  # Create user with referral
  createUserWithReferral(input: CreateUserWithReferralInput!): CreateUserResponse! @auth

  # Create new user
  createUser(input: CreateUserInput!): CreateUserResponse! @auth

  # Create user wallet
  createUserWallet(input: CreateUserWalletInput!): CreateUserWalletResponse! @auth

  # Update user first login status
  updateFirstLoginStatus(userId: ID!): User! @auth

  # Update wallet export status
  updateWalletExportStatus(userId: ID!): User! @auth

  # Create user invitation code
  createUserInvitationCode(input: CreateUserInvitationCodeInput!): CreateUserResponse! @auth

  # Validate referral eligibility
  validateReferralEligibility(userId: ID!, referrerId: ID!): ValidationResponse! @auth
}

type ValidationResponse {
  valid: Boolean!
  message: String!
}
